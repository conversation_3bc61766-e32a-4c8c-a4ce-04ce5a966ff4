package com.blog.common.validation;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.*;

/**
 * 安全输入验证注解
 */
@Target({ElementType.FIELD, ElementType.PARAMETER})
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = SafeInputValidator.class)
@Documented
public @interface SafeInput {
    
    String message() default "输入内容包含不安全字符";
    
    Class<?>[] groups() default {};
    
    Class<? extends Payload>[] payload() default {};
    
    /**
     * 是否允许HTML标签
     */
    boolean allowHtml() default false;
    
    /**
     * 最大长度
     */
    int maxLength() default Integer.MAX_VALUE;
    
    /**
     * 最小长度
     */
    int minLength() default 0;
}
