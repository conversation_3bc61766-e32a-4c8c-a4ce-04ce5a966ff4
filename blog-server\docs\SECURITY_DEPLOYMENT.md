# 后端安全部署指南

## 🔒 已修复的安全问题
1. **文件上传安全**：认证要求、文件验证、大小限制
2. **JWT安全**：强密钥、环境变量配置
3. **CORS配置**：限制来源域名
4. **数据库安全**：环境变量配置、强密码
5. **输入验证**：SQL注入和XSS防护
6. **安全头配置**：CSP、XSS保护等

## 🚀 生产环境部署检查清单

### 必须配置
- [ ] 创建 `.env.prod` 文件
- [ ] 设置强JWT密钥（32+字符）
- [ ] 配置强数据库密码
- [ ] 设置Redis密码（如果使用Redis）
- [ ] 配置CORS允许域名
- [ ] 启用HTTPS
- [ ] 创建专用数据库用户

## 📋 快速部署步骤

### 1. 环境变量配置
```bash
cp .env.example .env.prod
# 编辑 .env.prod，设置：
# JWT_SECRET=强密钥(32+字符)
# DB_PASSWORD=强数据库密码
# REDIS_PASSWORD=Redis密码(如果使用Redis)
# CORS_ALLOWED_ORIGINS=https://yourdomain.com
```

### 2. 数据库配置
```sql
CREATE DATABASE blog_system CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER 'blog_user'@'localhost' IDENTIFIED BY 'strong_password';
GRANT SELECT, INSERT, UPDATE, DELETE ON blog_system.* TO 'blog_user'@'localhost';
```

### 3. 启动应用
```bash
SPRING_PROFILES_ACTIVE=prod mvn spring-boot:run
```

## 🛡️ 安全建议

### 定期维护
- 更新依赖包
- 检查安全漏洞
- 审查访问日志
- 更新密钥和密码

### 监控告警
- 登录失败次数
- 文件上传异常
- 数据库连接异常
- 系统资源使用率

### 备份策略
- 每日自动备份数据库
- 定期备份应用配置
- 测试备份恢复流程

> **注意**：本指南提供基础安全配置，生产环境请根据实际情况加强。
