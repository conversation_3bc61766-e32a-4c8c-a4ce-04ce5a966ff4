# 通用配置
server:
  port: ${SERVER_PORT:8080}
  servlet:
    context-path: /api

# Spring配置
spring:
  # 环境配置
  profiles:
    active: ${SPRING_PROFILES_ACTIVE:dev}

  # 允许循环引用
  main:
    allow-circular-references: true

  # Jackson配置
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8

  # mvc配置
  mvc:
    pathmatch:
      matching-strategy: ant-path-matcher

# MyBatis-Plus通用配置
mybatis-plus:
  mapper-locations: classpath:mapper/*.xml
  type-aliases-package: com.blog.entity
  configuration:
    map-underscore-to-camel-case: true
  global-config:
    db-config:
      logic-delete-field: delFlag  # 逻辑删除字段
      logic-delete-value: 1 # 删除值
      logic-not-delete-value: 0 # 未删除值

# 日志配置
logging:
  level:
    com.blog: info
    org.springframework: warn 

# 管理端点配置
management:
  health:
    redis:
      enabled: false  # 禁用Redis健康检查 