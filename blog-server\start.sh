#!/bin/bash

# 博客系统启动脚本
# 使用方法: ./start.sh [dev|prod]

PROFILE=${1:-dev}

echo "=== 博客系统启动 ==="
echo "环境: $PROFILE"

# 检查Java环境
if ! command -v java &> /dev/null; then
    echo "错误: 未找到Java环境"
    exit 1
fi

# 检查Maven环境
if ! command -v mvn &> /dev/null; then
    echo "错误: 未找到Maven环境"
    exit 1
fi

# 设置环境变量
export SPRING_PROFILES_ACTIVE=$PROFILE

# 启动应用
echo "启动应用..."
mvn spring-boot:run -Dspring.profiles.active=$PROFILE
