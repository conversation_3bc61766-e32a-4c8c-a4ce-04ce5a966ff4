# 前端故障排除

## 常见问题及解决方案

### 1. 启动问题

#### 依赖安装失败
**症状**：
```
npm ERR! peer dep missing
```

**解决方案**：
- 删除 `node_modules` 和 `package-lock.json`
- 重新安装：`npm install`
- 或使用 `npm install --legacy-peer-deps`

#### 端口被占用
**症状**：
```
Port 3000 is already in use
```

**解决方案**：
- 修改 `vite.config.js` 中的端口配置
- 或停止占用3000端口的进程

#### Node版本不兼容
**症状**：
```
The engine "node" is incompatible with this module
```

**解决方案**：
- 升级Node.js到14+版本
- 或使用nvm切换Node版本

### 2. 开发问题

#### API请求失败
**症状**：前端无法获取数据，控制台显示网络错误

**解决方案**：
- 确认后端服务是否启动（http://localhost:8080/api）
- 检查代理配置是否正确
- 查看浏览器Network面板的具体错误

#### 跨域问题
**症状**：
```
CORS policy: No 'Access-Control-Allow-Origin' header
```

**解决方案**：
- 检查后端CORS配置
- 确认前端域名在后端允许列表中
- 开发环境使用代理配置

#### 路由404错误
**症状**：页面刷新后显示404

**解决方案**：
- 开发环境：检查路由配置
- 生产环境：配置服务器支持SPA路由

### 3. 构建问题

#### 构建失败
**症状**：
```
Build failed with errors
```

**解决方案**：
- 检查代码语法错误
- 确认所有依赖已正确安装
- 查看具体错误信息并修复

#### 内存不足
**症状**：
```
JavaScript heap out of memory
```

**解决方案**：
- 增加Node.js内存限制：`NODE_OPTIONS="--max-old-space-size=4096" npm run build`
- 优化代码，减少内存使用

#### 静态资源路径错误
**症状**：部署后资源加载失败

**解决方案**：
- 检查 `vite.config.js` 中的 `base` 配置
- 确认部署路径与配置一致

### 4. 运行时问题

#### 白屏问题
**症状**：页面显示空白

**解决方案**：
- 检查浏览器控制台错误信息
- 确认JavaScript文件是否正确加载
- 检查路由配置

#### 样式不生效
**症状**：页面样式显示异常

**解决方案**：
- 检查CSS文件是否正确引入
- 确认样式选择器优先级
- 检查scoped样式作用域

#### 组件不更新
**症状**：数据变化但页面不更新

**解决方案**：
- 检查响应式数据定义
- 确认使用正确的Vue 3 API
- 检查组件key值

### 5. 性能问题

#### 页面加载慢
**症状**：首屏加载时间过长

**解决方案**：
- 启用路由懒加载
- 优化图片资源
- 使用CDN加速

#### 内存泄漏
**症状**：页面使用时间长后变卡顿

**解决方案**：
- 及时清理事件监听器
- 正确使用组件生命周期
- 避免创建过多响应式对象

### 6. 兼容性问题

#### 浏览器兼容性
**症状**：某些浏览器功能异常

**解决方案**：
- 检查浏览器版本支持
- 添加必要的polyfill
- 使用兼容性更好的API

#### 移动端适配
**症状**：移动端显示异常

**解决方案**：
- 检查响应式样式
- 添加viewport meta标签
- 测试不同屏幕尺寸

## 调试技巧

### 1. 浏览器开发者工具
- **Console**：查看错误信息和调试输出
- **Network**：检查API请求和响应
- **Elements**：检查DOM结构和样式
- **Sources**：设置断点调试JavaScript

### 2. Vue DevTools
- 安装Vue DevTools浏览器扩展
- 查看组件树和状态
- 监控事件和性能

### 3. 网络调试
```javascript
// 在组件中添加调试信息
console.log('API Response:', response)
console.log('Component State:', this.state)
```

### 4. 性能分析
- 使用Performance面板分析性能
- 检查内存使用情况
- 分析包大小和加载时间

## 日志查看

### 开发环境
- 浏览器控制台
- Vue DevTools
- Network面板

### 生产环境
- 配置错误监控服务
- 收集用户反馈
- 分析访问日志

## 获取帮助

### 1. 文档资源
- [Vue.js官方文档](https://vuejs.org/)
- [Vite官方文档](https://vitejs.dev/)
- [Element Plus文档](https://element-plus.org/)

### 2. 社区支持
- Vue.js官方论坛
- Stack Overflow
- GitHub Issues

### 3. 项目支持
如果问题仍未解决：
1. 查看浏览器控制台完整错误信息
2. 检查相关配置文件
3. 提供复现步骤
4. 提交Issue并附上详细信息
