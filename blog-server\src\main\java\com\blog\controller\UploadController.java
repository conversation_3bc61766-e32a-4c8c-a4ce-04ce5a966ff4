package com.blog.controller;

import com.blog.common.api.Result;
import com.blog.config.SystemConfig;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;

/**
 * 文件上传控制器
 */
@RestController
@RequestMapping("/upload")
@Api(tags = "文件上传接口")
@Slf4j
public class UploadController {

    @Autowired
    private SystemConfig systemConfig;

    // 允许的图片文件扩展名
    private static final List<String> ALLOWED_IMAGE_EXTENSIONS = Arrays.asList(
            ".jpg", ".jpeg", ".png", ".gif", ".bmp", ".webp"
    );

    // 允许的图片MIME类型
    private static final List<String> ALLOWED_IMAGE_MIME_TYPES = Arrays.asList(
            "image/jpeg", "image/png", "image/gif", "image/bmp", "image/webp"
    );

    // 最大文件大小 (2MB)
    private static final long MAX_FILE_SIZE = 2 * 1024 * 1024;

    /**
     * 上传头像
     * @param file 文件
     * @return 上传结果
     */
    @ApiOperation("上传头像")
    @PostMapping("/avatar")
    @PreAuthorize("isAuthenticated()") // 需要认证
    public Result<String> uploadAvatar(@RequestParam("file") MultipartFile file) {
        try {
            // 基础验证
            Result<String> validationResult = validateFile(file);
            if (validationResult != null) {
                return validationResult;
            }

            // 验证文件是否为真实图片
            if (!isValidImageFile(file)) {
                return Result.failed("文件不是有效的图片格式");
            }

            // 创建上传目录
            String uploadDir = systemConfig.getUploadDir() + "/avatar";
            File dir = new File(uploadDir);
            if (!dir.exists()) {
                boolean created = dir.mkdirs();
                if (!created) {
                    log.error("创建上传目录失败: {}", uploadDir);
                    return Result.failed("创建上传目录失败");
                }
            }

            // 生成安全的文件名
            String newFilename = generateSecureFilename(file.getOriginalFilename());

            // 保存文件
            File destFile = new File(uploadDir, newFilename);
            file.transferTo(destFile);

            log.info("文件上传成功：{}, 大小: {} bytes", destFile.getAbsolutePath(), file.getSize());

            // 返回访问URL
            String fileUrl = "/api" + systemConfig.getResourceUrl() + "/avatar/" + newFilename;
            return Result.success(fileUrl, "上传成功");

        } catch (IOException e) {
            log.error("文件上传失败", e);
            return Result.failed("文件上传失败: " + e.getMessage());
        } catch (Exception e) {
            log.error("文件上传过程中发生未知错误", e);
            return Result.failed("文件上传失败");
        }
    }

    /**
     * 验证文件基础信息
     */
    private Result<String> validateFile(MultipartFile file) {
        // 检查文件是否为空
        if (file == null || file.isEmpty()) {
            log.warn("上传文件为空");
            return Result.failed("上传文件不能为空");
        }

        // 检查文件大小
        if (file.getSize() > MAX_FILE_SIZE) {
            log.warn("文件大小超出限制: {} bytes", file.getSize());
            return Result.failed("文件大小不能超过2MB");
        }

        // 获取原始文件名
        String originalFilename = file.getOriginalFilename();
        if (originalFilename == null || originalFilename.trim().isEmpty()) {
            return Result.failed("文件名不能为空");
        }

        // 检查文件扩展名
        String suffix = getFileExtension(originalFilename);
        if (!ALLOWED_IMAGE_EXTENSIONS.contains(suffix.toLowerCase())) {
            return Result.failed("不支持的文件格式，只支持: " + String.join(", ", ALLOWED_IMAGE_EXTENSIONS));
        }

        // 检查MIME类型
        String contentType = file.getContentType();
        if (contentType == null || !ALLOWED_IMAGE_MIME_TYPES.contains(contentType.toLowerCase())) {
            return Result.failed("不支持的文件类型");
        }

        return null; // 验证通过
    }

    /**
     * 验证文件是否为真实的图片文件
     */
    private boolean isValidImageFile(MultipartFile file) {
        try (InputStream inputStream = file.getInputStream()) {
            BufferedImage image = ImageIO.read(inputStream);
            return image != null && image.getWidth() > 0 && image.getHeight() > 0;
        } catch (IOException e) {
            log.warn("读取图片文件失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 生成安全的文件名
     */
    private String generateSecureFilename(String originalFilename) {
        String extension = getFileExtension(originalFilename);
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        String randomStr = UUID.randomUUID().toString().replace("-", "");
        return "avatar_" + timestamp + "_" + randomStr + extension;
    }

    /**
     * 获取文件扩展名
     */
    private String getFileExtension(String filename) {
        if (filename == null || !filename.contains(".")) {
            return "";
        }
        return filename.substring(filename.lastIndexOf("."));
    }
}