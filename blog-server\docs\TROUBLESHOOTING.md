# 故障排除

## 常见问题及解决方案

### 1. 应用启动失败

#### 数据库连接失败
**症状**：
```
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure
```

**解决方案**：
- 检查MySQL服务是否启动
- 验证数据库配置（用户名、密码、数据库名）
- 确认数据库已创建
- 检查防火墙设置

#### 端口被占用
**症状**：
```
Port 8080 was already in use
```

**解决方案**：
- 修改 `application.yml` 中的 `server.port`
- 或停止占用8080端口的进程

#### JWT密钥未设置
**症状**：
```
JWT_SECRET environment variable not set
```

**解决方案**：
- 检查 `.env` 文件是否存在
- 确保 `JWT_SECRET` 已设置且长度至少32字符

### 2. 文件上传问题

#### 文件上传失败
**症状**：返回401未认证错误

**解决方案**：
- 确保已登录（文件上传需要认证）
- 检查JWT token是否有效
- 验证请求头中是否包含 `Authorization: Bearer {token}`

#### 文件类型不支持
**症状**：返回"不支持的文件格式"错误

**解决方案**：
- 确保文件是图片格式（jpg, jpeg, png, gif, bmp, webp）
- 检查文件是否损坏

#### 文件过大
**症状**：返回"文件大小超出限制"错误

**解决方案**：
- 确保文件大小不超过2MB
- 压缩图片后重新上传

### 3. 认证问题

#### JWT验证失败
**症状**：API请求返回401错误

**解决方案**：
- 检查token是否过期（开发环境30分钟，生产环境1小时）
- 重新登录获取新token
- 确保请求头格式正确：`Authorization: Bearer {token}`

#### 权限不足
**症状**：返回403错误

**解决方案**：
- 确认当前用户是否有相应权限
- 管理员功能需要ADMIN角色

### 4. 数据库问题

#### 表不存在
**症状**：
```
Table 'blog_system.xxx' doesn't exist
```

**解决方案**：
- 确保数据库已创建
- 检查表结构是否正确
- 运行数据库初始化脚本

#### 字符编码问题
**症状**：中文显示乱码

**解决方案**：
- 确保数据库字符集为 `utf8mb4`
- 检查连接字符串中的编码设置

### 5. Redis问题

#### Redis连接失败
**症状**：
```
Unable to connect to Redis
```

**解决方案**：
- 检查Redis服务是否启动
- 验证Redis配置信息
- 如不使用Redis，可在配置中禁用

### 6. 配置问题

#### 环境变量未生效
**症状**：使用的仍是默认配置

**解决方案**：
- 确保 `.env` 文件在正确位置
- 检查环境变量名称是否正确
- 重启应用

#### CORS跨域问题
**症状**：前端请求被阻止

**解决方案**：
- 检查CORS配置中的允许域名
- 确保前端域名在允许列表中

## 日志查看

### 开发环境
- 控制台直接输出
- SQL日志已启用，可查看具体SQL语句

### 生产环境
- 日志文件：`/var/log/blog/blog-server.log`
- 使用 `tail -f` 实时查看日志

## 调试技巧

### 1. 启用详细日志
在 `application-dev.yml` 中设置：
```yaml
logging:
  level:
    com.blog: DEBUG
    org.springframework.security: DEBUG
```

### 2. 使用API测试工具
推荐使用Postman或其他API测试工具测试接口

### 3. 检查数据库连接
```bash
mysql -h localhost -u root -p blog_system
```

## 获取帮助

如果问题仍未解决：
1. 查看完整的错误日志
2. 检查相关配置文件
3. 确认环境变量设置
4. 提交Issue并附上详细错误信息
