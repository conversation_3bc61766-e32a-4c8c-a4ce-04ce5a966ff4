# 环境变量配置示例
# 复制此文件为 .env 并根据实际情况修改配置

# 应用环境 (dev, test, prod)
SPRING_PROFILES_ACTIVE=dev

# 服务器配置
SERVER_PORT=8080

# 数据库配置
DB_URL=***********************************************************************************************************************************************************************************************
DB_USERNAME=root
DB_PASSWORD=your_strong_password_here

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DATABASE=0
REDIS_PASSWORD=your_redis_password_here

# JWT配置 - 请使用强密钥
JWT_SECRET=your_very_strong_jwt_secret_key_at_least_32_characters_long_2024!@#$%^&*()
JWT_EXPIRATION=1800000

# 文件上传配置
FILE_UPLOAD_PATH=/var/blog/uploads

# 日志配置
LOG_FILE=/var/log/blog/blog-server.log

# Druid监控配置（生产环境）
DRUID_USERNAME=admin
DRUID_PASSWORD=your_druid_password_here

# CORS配置
CORS_ALLOWED_ORIGINS=https://yourdomain.com,https://www.yourdomain.com

# 安全配置
SECURITY_ENABLE_CSRF=false
SECURITY_ENABLE_FRAME_OPTIONS=true
SECURITY_CONTENT_SECURITY_POLICY=default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'

# 安全提示：
# 1. JWT_SECRET 应该是一个至少32个字符的强密钥
# 2. 所有密码都应该使用强密码
# 3. 生产环境中不要使用默认密码
# 4. 定期更换密钥和密码
# 5. CORS_ALLOWED_ORIGINS 应该只包含信任的域名
# 6. 生产环境建议启用HTTPS和相关安全头
