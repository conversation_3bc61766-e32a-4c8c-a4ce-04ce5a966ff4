package com.blog.dto;

import com.blog.common.validation.SafeInput;
import lombok.Data;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * 评论数据传输对象
 */
@Data
public class CommentDTO {

    /**
     * 评论内容
     */
    @NotBlank(message = "评论内容不能为空")
    @Size(min = 1, max = 1000, message = "评论内容长度必须在1-1000字符之间")
    @SafeInput(minLength = 1, maxLength = 1000, allowHtml = false)
    private String content;

    /**
     * 文章ID
     */
    @NotNull(message = "文章ID不能为空")
    private Long articleId;

    /**
     * 父评论ID，如果是顶级评论则为null
     */
    private Long parentId;

    /**
     * 回复用户ID
     */
    private Long toUserId;

    /**
     * 匿名用户昵称（匿名评论时必填）
     */
    @Size(max = 50, message = "昵称长度不能超过50字符")
    @SafeInput(maxLength = 50)
    private String anonymousNickname;

    /**
     * 匿名用户邮箱（匿名评论时必填）
     */
    @Email(message = "邮箱格式不正确")
    @Size(max = 100, message = "邮箱长度不能超过100字符")
    @SafeInput(maxLength = 100)
    private String anonymousEmail;
} 