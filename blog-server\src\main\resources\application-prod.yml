# 生产环境配置
server:
  port: ${SERVER_PORT:8080}
  servlet:
    context-path: /api

spring:
  # 允许循环引用
  main:
    allow-circular-references: true
  # 数据源配置
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ${DB_URL:**********************************************************************************************************************************************************************************************}
    username: ${DB_USERNAME}
    password: ${DB_PASSWORD}
    type: com.alibaba.druid.pool.DruidDataSource
    druid:
      # 初始化大小，最小，最大
      initial-size: 10
      min-idle: 10
      max-active: 50
      # 配置获取连接等待超时的时间
      max-wait: 60000
      # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
      time-between-eviction-runs-millis: 60000
      # 配置一个连接在池中最小生存的时间，单位是毫秒
      min-evictable-idle-time-millis: 300000
      # 启用监控统计
      filters: stat,wall,log4j2
      # 配置监控统计拦截的filters，去掉后监控界面sql无法统计
      web-stat-filter:
        enabled: true
        url-pattern: /*
        exclusions: "*.js,*.gif,*.jpg,*.png,*.css,*.ico,/druid/*"
      stat-view-servlet:
        enabled: true
        url-pattern: /druid/*
        reset-enable: false
        login-username: ${DRUID_USERNAME:admin}
        login-password: ${DRUID_PASSWORD}
  
  # Redis配置
  redis:
    host: ${REDIS_HOST}
    port: ${REDIS_PORT:6379}
    database: ${REDIS_DATABASE:0}
    password: ${REDIS_PASSWORD}
    timeout: 10000
    lettuce:
      pool:
        max-active: 20
        max-wait: -1
        max-idle: 10
        min-idle: 0
    
  # Jackson配置
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
    
  # mvc配置
  mvc:
    pathmatch:
      matching-strategy: ant-path-matcher

# MyBatis-Plus配置
mybatis-plus:
  mapper-locations: classpath:mapper/*.xml
  type-aliases-package: com.blog.entity
  configuration:
    map-underscore-to-camel-case: true
    # 生产环境关闭SQL日志
    # log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      logic-delete-field: delFlag  # 逻辑删除字段
      logic-delete-value: 1 # 删除值
      logic-not-delete-value: 0 # 未删除值
      
# JWT配置
jwt:
  # JWT密钥 - 生产环境必须使用环境变量
  secret: ${JWT_SECRET}
  # JWT过期时间（毫秒）
  expiration: ${JWT_EXPIRATION:3600000}  # 1小时
  # JWT头部
  tokenHeader: Authorization
  # JWT负载中拿到开头
  tokenHead: Bearer

# 文件上传配置
file:
  upload:
    path: ${FILE_UPLOAD_PATH:/var/blog/uploads}
  access:
    path: /upload/
    
# 系统配置
system:
  uploadDir: ${FILE_UPLOAD_PATH:/var/blog/uploads}
  resourceUrl: /upload

# CORS配置
cors:
  allowed-origins: ${CORS_ALLOWED_ORIGINS:https://yourdomain.com}
  allowed-methods: GET,POST,PUT,DELETE,OPTIONS
  allowed-headers: Authorization,Content-Type,X-Requested-With
  exposed-headers: Authorization,Content-Type
  allow-credentials: true
  max-age: 3600

# 日志配置
logging:
  level:
    root: WARN
    com.blog: INFO
    org.springframework.security: WARN
  file:
    name: ${LOG_FILE:/var/log/blog/blog-server.log}
  pattern:
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
