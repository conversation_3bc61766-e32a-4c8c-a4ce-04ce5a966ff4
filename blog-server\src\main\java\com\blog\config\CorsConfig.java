package com.blog.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;
import org.springframework.web.filter.CorsFilter;

import java.util.Arrays;
import java.util.List;

/**
 * 跨域配置
 */
@Configuration
public class CorsConfig {

    @Value("${cors.allowed-origins:http://localhost:3000,http://127.0.0.1:3000}")
    private String allowedOrigins;

    @Value("${cors.allowed-methods:GET,POST,PUT,DELETE,OPTIONS}")
    private String allowedMethods;

    @Value("${cors.allowed-headers:*}")
    private String allowedHeaders;

    @Value("${cors.exposed-headers:Authorization,Content-Type}")
    private String exposedHeaders;

    @Value("${cors.allow-credentials:true}")
    private boolean allowCredentials;

    @Value("${cors.max-age:3600}")
    private long maxAge;

    /**
     * 跨域配置过滤器
     */
    @Bean
    public CorsFilter corsFilter() {
        CorsConfiguration config = new CorsConfiguration();

        // 解析允许的来源
        List<String> origins = Arrays.asList(allowedOrigins.split(","));
        for (String origin : origins) {
            config.addAllowedOrigin(origin.trim());
        }

        // 如果是开发环境，允许localhost的任意端口
        String activeProfile = System.getProperty("spring.profiles.active", "dev");
        if ("dev".equals(activeProfile)) {
            config.addAllowedOriginPattern("http://localhost:*");
            config.addAllowedOriginPattern("http://127.0.0.1:*");
            System.out.println("开发环境：允许localhost任意端口的跨域请求");
        }

        // 设置允许的请求方法
        List<String> methods = Arrays.asList(allowedMethods.split(","));
        for (String method : methods) {
            config.addAllowedMethod(method.trim());
        }

        // 设置允许的请求头
        if ("*".equals(allowedHeaders)) {
            config.addAllowedHeader("*");
        } else {
            List<String> headers = Arrays.asList(allowedHeaders.split(","));
            for (String header : headers) {
                config.addAllowedHeader(header.trim());
            }
        }

        // 设置暴露的响应头
        List<String> exposedHeadersList = Arrays.asList(exposedHeaders.split(","));
        for (String header : exposedHeadersList) {
            config.addExposedHeader(header.trim());
        }

        // 是否允许发送Cookie
        config.setAllowCredentials(allowCredentials);

        // 预检请求的缓存时间
        config.setMaxAge(maxAge);

        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        source.registerCorsConfiguration("/**", config);

        System.out.println("CORS配置已加载:");
        System.out.println("  允许的来源: " + origins);
        System.out.println("  允许的方法: " + methods);
        System.out.println("  允许凭证: " + allowCredentials);
        System.out.println("  当前环境: " + activeProfile);

        return new CorsFilter(source);
    }
}