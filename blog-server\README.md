# 博客系统后端

基于Spring Boot的博客系统后端API服务，具备完整的安全防护机制。

## 🚀 快速开始

### 环境要求
- Java 8+
- Maven 3.6+
- MySQL 5.7+
- Redis (可选)

### 配置环境
```bash
# 1. 复制环境变量模板
cp .env.example .env

# 2. 编辑环境变量（必须设置）
# JWT_SECRET=强密钥(32+字符)
# DB_PASSWORD=强数据库密码(开发环境默认12345)
# REDIS_PASSWORD=Redis密码(如果使用Redis)
```

### 创建数据库
```sql
CREATE DATABASE blog_system CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

### 启动应用
```bash
# 开发环境
mvn spring-boot:run

# 或使用启动脚本
./start.sh dev    # Linux/Mac
start.bat dev     # Windows
```

### 访问地址
- API服务：http://localhost:8080/api

## 🔐 安全特性

- ✅ 文件上传安全验证（认证+类型+大小检查）
- ✅ JWT强密钥保护
- ✅ CORS跨域限制
- ✅ SQL注入和XSS防护
- ✅ 安全响应头配置
- ✅ 输入验证注解

## 📁 项目结构

```
blog-server/
├── src/main/java/com/blog/
│   ├── controller/          # 控制器层
│   ├── service/            # 业务逻辑层
│   ├── entity/             # 实体类
│   ├── dto/                # 数据传输对象
│   ├── config/             # 配置类
│   ├── security/           # 安全相关
│   └── common/             # 公共组件
├── src/main/resources/
│   ├── mapper/             # MyBatis映射文件
│   ├── application.yml     # 主配置文件
│   ├── application-dev.yml # 开发环境配置
│   └── application-prod.yml# 生产环境配置
├── docs/                   # 文档目录
├── .env.example           # 环境变量模板
├── start.sh               # 启动脚本(Linux/Mac)
└── start.bat              # 启动脚本(Windows)
```

## 📖 文档

- [安全部署指南](docs/SECURITY_DEPLOYMENT.md)
- [API文档](docs/API.md)
- [故障排除](docs/TROUBLESHOOTING.md)

## 🛠️ 开发

### 初始化数据
首次启动后，需要通过注册接口创建管理员账号，然后手动修改数据库中该用户的role字段为'admin'。

### 常用命令
```bash
# 编译
mvn compile

# 测试
mvn test

# 打包
mvn package

# 清理
mvn clean
```

## 📞 技术栈

- **框架**：Spring Boot 2.7.15
- **安全**：Spring Security + JWT
- **数据库**：MySQL + MyBatis Plus
- **缓存**：Redis (可选)
- **监控**：Spring Boot Actuator
- **构建**：Maven
