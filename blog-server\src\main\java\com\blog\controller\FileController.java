package com.blog.controller;

import com.blog.common.api.Result;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.UUID;

/**
 * 文件上传控制器
 */
@RestController
@RequestMapping("/upload")
public class FileController {
    private static final Logger logger = LoggerFactory.getLogger(FileController.class);

    // 文件上传目录
    @Value("${file.upload.path:uploads}")
    private String uploadPath;

    // 文件访问URL前缀
    @Value("${file.access.path:/upload/}")
    private String accessPath;

    /**
     * 上传文件
     * @param file 文件
     * @return 文件访问URL
     */
    @PostMapping
    @PreAuthorize("isAuthenticated()") // 需要认证
    public Result<?> upload(@RequestParam("file") MultipartFile file) {
        // 检查文件是否为空
        if (file.isEmpty()) {
            logger.error("上传文件为空");
            return Result.failed("上传文件不能为空");
        }

        try {
            // 获取项目根目录
            String projectPath = System.getProperty("user.dir");
            logger.info("项目根目录: {}", projectPath);
            
            // 创建上传目录
            String datePath = new SimpleDateFormat("yyyy/MM/dd").format(new Date());
            String uploadDir = projectPath + File.separator + uploadPath + File.separator + datePath.replace("/", File.separator);
            logger.info("上传目录: {}", uploadDir);
            
            // 确保目录存在
            File directory = new File(uploadDir);
            if (!directory.exists()) {
                boolean created = directory.mkdirs();
                logger.info("创建目录结果: {}", created);
                if (!created) {
                    logger.error("创建上传目录失败");
                    return Result.failed("创建上传目录失败");
                }
            }

            // 生成文件名
            String originalFilename = file.getOriginalFilename();
            String extension = originalFilename.substring(originalFilename.lastIndexOf("."));
            String fileName = UUID.randomUUID().toString().replaceAll("-", "") + extension;
            logger.info("生成文件名: {}", fileName);

            // 保存文件
            File destFile = new File(directory, fileName);
            logger.info("目标文件路径: {}", destFile.getAbsolutePath());
            file.transferTo(destFile);
            logger.info("文件保存成功");

            // 构建完整的访问URL（包括上下文路径）
            String fileUrl = accessPath + datePath.replace("\\", "/") + "/" + fileName;
            logger.info("文件访问URL: {}", fileUrl);
            
            // 打印更多详细信息，帮助调试
            logger.info("文件上传配置信息：");
            logger.info("上传路径配置(uploadPath): {}", uploadPath);
            logger.info("访问路径配置(accessPath): {}", accessPath);
            logger.info("项目根目录: {}", projectPath);
            logger.info("完整的物理存储路径: {}", destFile.getAbsolutePath());
            
            // 返回完整URL
            return Result.success(fileUrl);
        } catch (IOException e) {
            logger.error("文件上传失败", e);
            return Result.failed("文件上传失败：" + e.getMessage());
        }
    }
} 