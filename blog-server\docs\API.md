# API 文档

## 基础信息

- **Base URL**: `http://localhost:8080/api`
- **认证方式**: JWT Token
- **Content-Type**: `application/json`

## 认证

### 登录
```http
POST /auth/login
Content-Type: application/json

{
  "username": "admin",
  "password": "admin123"
}
```

**响应**:
```json
{
  "code": 200,
  "message": "登录成功",
  "data": {
    "token": "eyJhbGciOiJIUzI1NiJ9...",
    "user": {
      "id": 1,
      "username": "admin",
      "nickname": "管理员"
    }
  }
}
```

### 注册
```http
POST /auth/register
Content-Type: application/json

{
  "username": "newuser",
  "password": "password123",
  "email": "<EMAIL>",
  "nickname": "新用户"
}
```

## 文章管理

### 获取文章列表
```http
GET /articles?page=1&size=10&categoryId=1&keyword=搜索关键词
```

### 获取文章详情
```http
GET /articles/{id}
```

### 创建文章（需要认证）
```http
POST /articles
Authorization: Bearer {token}
Content-Type: application/json

{
  "title": "文章标题",
  "content": "文章内容",
  "summary": "文章摘要",
  "categoryId": 1,
  "tagIds": [1, 2, 3],
  "status": 1
}
```

### 更新文章（需要认证）
```http
PUT /articles/{id}
Authorization: Bearer {token}
Content-Type: application/json

{
  "title": "更新的标题",
  "content": "更新的内容"
}
```

### 删除文章（需要认证）
```http
DELETE /articles/{id}
Authorization: Bearer {token}
```

## 文件上传

### 上传头像（需要认证）
```http
POST /upload/avatar
Authorization: Bearer {token}
Content-Type: multipart/form-data

file: [图片文件]
```

**限制**:
- 文件大小：最大2MB
- 文件类型：jpg, jpeg, png, gif, bmp, webp
- 需要登录认证

## 评论管理

### 获取文章评论
```http
GET /comments/article/{articleId}?page=1&size=10
```

### 添加评论
```http
POST /comments
Content-Type: application/json

{
  "articleId": 1,
  "content": "评论内容",
  "parentId": null,
  "anonymousNickname": "匿名用户",
  "anonymousEmail": "<EMAIL>"
}
```

## 分类和标签

### 获取分类列表
```http
GET /categories
```

### 获取标签列表
```http
GET /tags
```

## 响应格式

### 成功响应
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {}
}
```

### 错误响应
```json
{
  "code": 400,
  "message": "错误信息",
  "data": null
}
```

## 状态码

- `200` - 成功
- `400` - 请求参数错误
- `401` - 未认证
- `403` - 权限不足
- `404` - 资源不存在
- `500` - 服务器内部错误

## API测试

推荐使用Postman、Insomnia或其他API测试工具进行接口测试。
