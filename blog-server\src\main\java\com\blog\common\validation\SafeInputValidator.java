package com.blog.common.validation;

import com.blog.common.utils.ValidationUtil;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

/**
 * 安全输入验证器
 */
public class SafeInputValidator implements ConstraintValidator<SafeInput, String> {

    private boolean allowHtml;
    private int maxLength;
    private int minLength;

    @Override
    public void initialize(SafeInput constraintAnnotation) {
        this.allowHtml = constraintAnnotation.allowHtml();
        this.maxLength = constraintAnnotation.maxLength();
        this.minLength = constraintAnnotation.minLength();
    }

    @Override
    public boolean isValid(String value, ConstraintValidatorContext context) {
        if (value == null) {
            return minLength == 0;
        }

        // 检查长度
        if (!ValidationUtil.isValidLength(value, minLength, maxLength)) {
            context.disableDefaultConstraintViolation();
            context.buildConstraintViolationWithTemplate(
                String.format("输入长度必须在%d到%d之间", minLength, maxLength)
            ).addConstraintViolation();
            return false;
        }

        // 检查SQL注入
        if (ValidationUtil.containsSqlInjection(value)) {
            context.disableDefaultConstraintViolation();
            context.buildConstraintViolationWithTemplate("输入内容包含不安全的SQL关键词")
                   .addConstraintViolation();
            return false;
        }

        // 检查XSS攻击
        if (!allowHtml && ValidationUtil.containsXss(value)) {
            context.disableDefaultConstraintViolation();
            context.buildConstraintViolationWithTemplate("输入内容包含不安全的脚本代码")
                   .addConstraintViolation();
            return false;
        }

        return true;
    }
}
