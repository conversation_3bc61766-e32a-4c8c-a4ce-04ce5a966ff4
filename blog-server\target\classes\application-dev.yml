# 开发环境配置
server:
  port: 8080
  servlet:
    context-path: /api

spring:
  # 允许循环引用
  main:
    allow-circular-references: true
  # 数据源配置
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ***********************************************************************************************************************************************************************************************
    username: ${DB_USERNAME:root}
    password: ${DB_PASSWORD:12345}
    type: com.alibaba.druid.pool.DruidDataSource
    druid:
      # 初始化大小，最小，最大
      initial-size: 5
      min-idle: 5
      max-active: 20
      # 配置获取连接等待超时的时间
      max-wait: 60000
      # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
      time-between-eviction-runs-millis: 60000
      # 配置一个连接在池中最小生存的时间，单位是毫秒
      min-evictable-idle-time-millis: 300000
  
  # Redis配置
  redis:
    host: ${REDIS_HOST:localhost}
    port: ${REDIS_PORT:6379}
    database: ${REDIS_DATABASE:0}
    password: ${REDIS_PASSWORD:}
    timeout: 10000
    
  # Jackson配置
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
    
  # mvc配置
  mvc:
    pathmatch:
      matching-strategy: ant-path-matcher

# MyBatis-Plus配置
mybatis-plus:
  mapper-locations: classpath:mapper/*.xml
  type-aliases-package: com.blog.entity
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl # 开发环境启用SQL日志
  global-config:
    db-config:
      logic-delete-field: delFlag  # 逻辑删除字段
      logic-delete-value: 1 # 删除值
      logic-not-delete-value: 0 # 未删除值
      
# JWT配置
jwt:
  # JWT密钥 - 开发环境使用环境变量或默认值
  secret: ${JWT_SECRET:blogSystemDevelopmentSecretKey2024!@#$%^&*()_+}
  # JWT过期时间（毫秒）
  expiration: ${JWT_EXPIRATION:1800000}  # 半小时
  # JWT头部
  tokenHeader: Authorization
  # JWT负载中拿到开头
  tokenHead: Bearer

# 文件上传配置
file:
  upload:
    path: uploads
  access:
    path: /upload/
    
# 系统配置
system:
  uploadDir: ${user.dir}/uploads
  resourceUrl: /upload

# CORS配置
cors:
  allowed-origins: http://localhost:3000,http://127.0.0.1:3000,http://localhost:5173
  allowed-methods: GET,POST,PUT,DELETE,OPTIONS
  allowed-headers: "*"
  exposed-headers: Authorization,Content-Type
  allow-credentials: true
  max-age: 3600

# 日志配置
logging:
  level:
    com.blog: DEBUG
    org.springframework.security: DEBUG
