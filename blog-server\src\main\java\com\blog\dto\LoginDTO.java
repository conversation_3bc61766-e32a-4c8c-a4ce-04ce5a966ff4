package com.blog.dto;

import com.blog.common.validation.SafeInput;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 登录DTO
 */
@Data
public class LoginDTO {
    /**
     * 用户名
     */
    @NotBlank(message = "用户名不能为空")
    @SafeInput(maxLength = 50)
    private String username;

    /**
     * 密码
     */
    @NotBlank(message = "密码不能为空")
    @SafeInput(maxLength = 100)
    private String password;
} 