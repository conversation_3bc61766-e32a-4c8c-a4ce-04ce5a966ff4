package com.blog.common.utils;

import org.springframework.util.StringUtils;

import java.util.regex.Pattern;

/**
 * 输入验证工具类
 */
public class ValidationUtil {

    // 邮箱正则表达式
    private static final Pattern EMAIL_PATTERN = Pattern.compile(
            "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$"
    );

    // 用户名正则表达式（只允许字母、数字、下划线，3-20位）
    private static final Pattern USERNAME_PATTERN = Pattern.compile(
            "^[a-zA-Z0-9_]{3,20}$"
    );

    // 密码正则表达式（至少8位，包含字母和数字）
    private static final Pattern PASSWORD_PATTERN = Pattern.compile(
            "^(?=.*[a-zA-Z])(?=.*\\d)[a-zA-Z\\d@$!%*?&]{8,}$"
    );

    // SQL注入关键词
    private static final String[] SQL_INJECTION_KEYWORDS = {
            "select", "insert", "update", "delete", "drop", "create", "alter",
            "exec", "execute", "union", "script", "javascript", "vbscript",
            "onload", "onerror", "onclick", "alert", "confirm", "prompt"
    };

    // XSS攻击关键词
    private static final String[] XSS_KEYWORDS = {
            "<script", "</script>", "javascript:", "vbscript:", "onload=",
            "onerror=", "onclick=", "onmouseover=", "onfocus=", "onblur=",
            "alert(", "confirm(", "prompt(", "document.cookie", "document.write"
    };

    /**
     * 验证邮箱格式
     */
    public static boolean isValidEmail(String email) {
        return StringUtils.hasText(email) && EMAIL_PATTERN.matcher(email).matches();
    }

    /**
     * 验证用户名格式
     */
    public static boolean isValidUsername(String username) {
        return StringUtils.hasText(username) && USERNAME_PATTERN.matcher(username).matches();
    }

    /**
     * 验证密码强度
     */
    public static boolean isValidPassword(String password) {
        return StringUtils.hasText(password) && PASSWORD_PATTERN.matcher(password).matches();
    }

    /**
     * 检查是否包含SQL注入关键词
     */
    public static boolean containsSqlInjection(String input) {
        if (!StringUtils.hasText(input)) {
            return false;
        }
        
        String lowerInput = input.toLowerCase();
        for (String keyword : SQL_INJECTION_KEYWORDS) {
            if (lowerInput.contains(keyword)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 检查是否包含XSS攻击代码
     */
    public static boolean containsXss(String input) {
        if (!StringUtils.hasText(input)) {
            return false;
        }
        
        String lowerInput = input.toLowerCase();
        for (String keyword : XSS_KEYWORDS) {
            if (lowerInput.contains(keyword)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 清理HTML标签（基础版本）
     */
    public static String cleanHtml(String input) {
        if (!StringUtils.hasText(input)) {
            return input;
        }
        
        // 移除所有HTML标签
        return input.replaceAll("<[^>]*>", "");
    }

    /**
     * 转义HTML特殊字符
     */
    public static String escapeHtml(String input) {
        if (!StringUtils.hasText(input)) {
            return input;
        }
        
        return input.replace("&", "&amp;")
                   .replace("<", "&lt;")
                   .replace(">", "&gt;")
                   .replace("\"", "&quot;")
                   .replace("'", "&#x27;")
                   .replace("/", "&#x2F;");
    }

    /**
     * 验证文件名安全性
     */
    public static boolean isValidFilename(String filename) {
        if (!StringUtils.hasText(filename)) {
            return false;
        }
        
        // 检查是否包含路径遍历字符
        if (filename.contains("..") || filename.contains("/") || filename.contains("\\")) {
            return false;
        }
        
        // 检查是否包含特殊字符
        return filename.matches("^[a-zA-Z0-9._-]+$");
    }

    /**
     * 验证URL安全性
     */
    public static boolean isValidUrl(String url) {
        if (!StringUtils.hasText(url)) {
            return false;
        }
        
        // 只允许http和https协议
        return url.startsWith("http://") || url.startsWith("https://");
    }

    /**
     * 验证文本长度
     */
    public static boolean isValidLength(String text, int minLength, int maxLength) {
        if (text == null) {
            return minLength == 0;
        }
        
        int length = text.length();
        return length >= minLength && length <= maxLength;
    }

    /**
     * 综合安全检查
     */
    public static boolean isSafeInput(String input) {
        return !containsSqlInjection(input) && !containsXss(input);
    }
}
