@echo off
REM 博客系统启动脚本 (Windows)
REM 使用方法: start.bat [dev|prod]

set PROFILE=%1
if "%PROFILE%"=="" set PROFILE=dev

echo === 博客系统启动 ===
echo 环境: %PROFILE%

REM 检查Java环境
java -version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Java环境
    exit /b 1
)

REM 检查Maven环境
mvn -version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Maven环境
    exit /b 1
)

REM 设置环境变量
set SPRING_PROFILES_ACTIVE=%PROFILE%

echo 启动应用...
mvn spring-boot:run -Dspring.profiles.active=%PROFILE%
