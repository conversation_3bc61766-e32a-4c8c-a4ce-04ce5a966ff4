# 博客系统前端

基于Vue.js 3的现代化博客系统前端界面。

## 🚀 快速开始

### 环境要求
- Node.js 14+
- npm 或 yarn

### 安装依赖
```bash
npm install
# 或
yarn install
```

### 启动开发服务器
```bash
npm run dev
# 或
yarn dev
```

### 构建生产版本
```bash
npm run build
# 或
yarn build
```

### 访问地址
- 开发环境：http://localhost:3000
- 后端API：http://localhost:8080/api

## 📁 项目结构

```
blog-web/
├── src/
│   ├── api/                # API接口
│   ├── assets/             # 静态资源
│   ├── components/         # 公共组件
│   ├── config/             # 配置文件
│   ├── router/             # 路由配置
│   ├── store/              # 状态管理
│   ├── utils/              # 工具函数
│   ├── views/              # 页面组件
│   ├── App.vue             # 根组件
│   └── main.js             # 入口文件
├── docs/                   # 文档目录
├── cypress/                # E2E测试
│   ├── e2e/               # 测试用例
│   ├── fixtures/          # 测试数据
│   └── support/           # 测试支持文件
├── package.json            # 项目配置
└── vite.config.js          # Vite配置
```

## 🛠️ 技术栈

- **框架**：Vue.js 3
- **构建工具**：Vite
- **UI组件库**：Element Plus
- **状态管理**：Pinia
- **路由**：Vue Router 4
- **HTTP客户端**：Axios
- **CSS预处理器**：Sass
- **测试**：Cypress

## 📖 功能特性

### 用户功能
- ✅ 用户注册/登录
- ✅ 文章浏览和搜索
- ✅ 评论系统
- ✅ 文章收藏和点赞
- ✅ 个人资料管理

### 管理功能
- ✅ 文章管理（增删改查）
- ✅ 分类和标签管理
- ✅ 评论管理
- ✅ 用户管理
- ✅ 系统设置

### 界面特性
- ✅ 响应式设计
- ✅ 现代化UI设计
- ✅ 毛玻璃效果
- ✅ 平滑动画过渡

## 🔧 开发

### 开发命令
```bash
# 启动开发服务器
npm run dev

# 构建生产版本
npm run build

# 预览构建结果
npm run preview

# 代码检查
npm run lint
```

### 环境配置
开发环境会自动代理API请求到后端服务器，配置在 `vite.config.js` 中：

```javascript
server: {
  proxy: {
    '/api': {
      target: 'http://localhost:8080',
      changeOrigin: true
    }
  }
}
```

## 📖 文档

- [开发指南](docs/DEVELOPMENT.md)
- [故障排除](docs/TROUBLESHOOTING.md)

## 🎨 自定义主题

项目支持主题自定义，可以在 `src/assets/styles/index.scss` 中修改CSS变量来自定义主题。

## 📱 浏览器支持

- Chrome >= 87
- Firefox >= 78
- Safari >= 14
- Edge >= 88

## 🤝 贡献

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request
