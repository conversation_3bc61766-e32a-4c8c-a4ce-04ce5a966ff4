# 前端开发指南

## 开发环境搭建

### 1. 环境要求
- Node.js 14+ 
- npm 6+ 或 yarn 1.22+
- 现代浏览器（Chrome、Firefox、Safari、Edge）

### 2. 项目初始化
```bash
# 克隆项目
git clone <repository-url>
cd blog-web

# 安装依赖
npm install

# 启动开发服务器
npm run dev
```

### 3. 开发服务器配置
开发服务器默认运行在 `http://localhost:3000`，并自动代理API请求到后端。

## 项目架构

### 目录结构说明
```
src/
├── api/                    # API接口层
│   ├── article.js         # 文章相关API
│   ├── auth.js            # 认证相关API
│   ├── category.js        # 分类相关API
│   └── ...                # 其他模块API
├── assets/                # 静态资源
│   ├── images/           # 图片资源
│   ├── icons/            # 图标资源
│   └── styles/           # 样式文件
├── components/            # 公共组件
│   ├── common/           # 通用组件
│   ├── layout/           # 布局组件
│   └── business/         # 业务组件
├── config/               # 配置文件
├── router/               # 路由配置
├── store/                # 状态管理
│   └── user.js           # 用户状态管理
├── utils/                # 工具函数
├── views/                # 页面组件
└── App.vue               # 根组件
```

## 开发规范

### 1. 命名规范
- **文件名**：使用 kebab-case（短横线分隔）
- **组件名**：使用 PascalCase
- **变量名**：使用 camelCase
- **常量名**：使用 UPPER_SNAKE_CASE

### 2. 组件开发规范
```vue
<template>
  <!-- 模板内容 -->
</template>

<script>
import { defineComponent } from 'vue'

export default defineComponent({
  name: 'ComponentName',
  props: {
    // props定义
  },
  emits: ['event-name'],
  setup(props, { emit }) {
    // 组合式API逻辑
    return {
      // 返回响应式数据和方法
    }
  }
})
</script>

<style lang="scss" scoped>
/* 组件样式 */
</style>
```

### 3. API调用规范
```javascript
// api/article.js
import request from '@/utils/request'

export const articleApi = {
  // 获取文章列表
  getList(params) {
    return request.get('/articles', { params })
  },
  
  // 获取文章详情
  getDetail(id) {
    return request.get(`/articles/${id}`)
  },
  
  // 创建文章
  create(data) {
    return request.post('/articles', data)
  }
}
```

### 4. 状态管理规范
```javascript
// store/user.js
import { defineStore } from 'pinia'
import { authApi } from '@/api/auth'

export const useUserStore = defineStore('user', {
  state: () => ({
    token: null,
    userInfo: null,
    isLoggedIn: false
  }),

  actions: {
    async login(credentials) {
      try {
        const { data } = await authApi.login(credentials)
        this.token = data.token
        this.userInfo = data.user
        this.isLoggedIn = true
      } catch (error) {
        throw error
      }
    }
  }
})
```

## 样式开发

### 1. 样式架构
```
assets/styles/
└── index.scss            # 主样式文件（包含CSS变量定义）
```

### 2. 主题变量
项目使用CSS变量进行主题配置，在 `src/assets/styles/index.scss` 中定义：

```scss
:root {
  /* 主色调 */
  --primary-color: #667eea;
  --primary-light: #764ba2;
  --primary-dark: #5a67d8;
  --secondary-color: #f093fb;

  /* 文本颜色 */
  --text-primary: #2d3748;
  --text-secondary: #4a5568;
  --text-muted: #718096;
}
```

## 路由配置

### 1. 路由结构
```javascript
// router/index.js
const routes = [
  {
    path: '/',
    component: Layout,
    children: [
      {
        path: '',
        name: 'Home',
        component: () => import('@/views/Home.vue'),
        meta: { title: '首页' }
      }
    ]
  },
  {
    path: '/admin',
    component: AdminLayout,
    meta: { requiresAuth: true, roles: ['admin'] },
    children: [
      // 管理页面路由
    ]
  }
]
```

### 2. 路由守卫
```javascript
// 全局前置守卫
router.beforeEach((to, from, next) => {
  // 权限检查
  if (to.meta.requiresAuth) {
    // 检查登录状态
  }
  next()
})
```

## 组件开发

### 1. 组件分类
- **基础组件**：通用UI组件（按钮、输入框等）
- **业务组件**：特定业务逻辑组件
- **布局组件**：页面布局相关组件

### 2. 组件通信
- **父子通信**：props + emit
- **跨组件通信**：provide/inject 或 Pinia
- **全局状态**：Pinia store

## 测试

### E2E测试
项目使用Cypress进行端到端测试：

```bash
# 安装Cypress（如果未安装）
npm install cypress --save-dev

# 交互式运行测试
npx cypress open

# 命令行运行测试
npx cypress run
```

## 构建部署

### 1. 构建命令
```bash
# 生产环境构建
npm run build

# 预览构建结果
npm run preview
```

### 2. 环境变量
```bash
# 复制环境变量模板
cp .env.example .env.development
cp .env.example .env.production

# .env.development
VITE_API_BASE_URL=http://localhost:8080
VITE_APP_TITLE=博客系统（开发）
APP_ENV=development

# .env.production
VITE_API_BASE_URL=https://yourdomain.com
VITE_APP_TITLE=博客系统
APP_ENV=production
```

## 性能优化

### 1. 代码分割
- 路由懒加载
- 组件懒加载
- 第三方库按需引入

### 2. 资源优化
- 图片压缩和懒加载
- CSS和JS压缩
- Gzip压缩

### 3. 缓存策略
- HTTP缓存
- 浏览器缓存
- CDN缓存

## 调试技巧

### 1. Vue DevTools
安装Vue DevTools浏览器扩展进行调试

### 2. 网络调试
使用浏览器开发者工具的Network面板查看API请求

### 3. 性能分析
使用Performance面板分析页面性能
