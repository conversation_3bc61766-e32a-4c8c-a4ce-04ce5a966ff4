package com.blog.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * 安全头配置
 */
@Configuration
public class SecurityHeadersConfig {

    @Value("${security.content-security-policy:default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'}")
    private String contentSecurityPolicy;

    @Value("${security.enable-frame-options:true}")
    private boolean enableFrameOptions;

    @Value("${security.enable-xss-protection:true}")
    private boolean enableXssProtection;

    @Value("${security.enable-content-type-options:true}")
    private boolean enableContentTypeOptions;

    @Value("${security.enable-hsts:false}")
    private boolean enableHsts;

    @Bean
    public OncePerRequestFilter securityHeadersFilter() {
        return new OncePerRequestFilter() {
            @Override
            protected void doFilterInternal(HttpServletRequest request, 
                                          HttpServletResponse response, 
                                          FilterChain filterChain) throws ServletException, IOException {
                
                // 添加安全头
                addSecurityHeaders(response);
                
                filterChain.doFilter(request, response);
            }
        };
    }

    private void addSecurityHeaders(HttpServletResponse response) {
        // Content Security Policy
        if (contentSecurityPolicy != null && !contentSecurityPolicy.isEmpty()) {
            response.setHeader("Content-Security-Policy", contentSecurityPolicy);
        }

        // X-Frame-Options
        if (enableFrameOptions) {
            response.setHeader("X-Frame-Options", "DENY");
        }

        // X-XSS-Protection
        if (enableXssProtection) {
            response.setHeader("X-XSS-Protection", "1; mode=block");
        }

        // X-Content-Type-Options
        if (enableContentTypeOptions) {
            response.setHeader("X-Content-Type-Options", "nosniff");
        }

        // HTTP Strict Transport Security (HSTS)
        if (enableHsts) {
            response.setHeader("Strict-Transport-Security", "max-age=31536000; includeSubDomains");
        }

        // Referrer Policy
        response.setHeader("Referrer-Policy", "strict-origin-when-cross-origin");

        // Permissions Policy
        response.setHeader("Permissions-Policy", "geolocation=(), microphone=(), camera=()");
    }
}
